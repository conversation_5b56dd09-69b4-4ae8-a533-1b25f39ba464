import os
from collections import defaultdict
from functools import cache
from typing import List, Dict, Optional

from langchain_core.documents import Document
from pymilvus import MilvusClient, DataType

from settings import get_or_creat_settings_ins
from src.helper.client.rerank import rerank
from src.log import logger
from src.modules.llms import LLMFactory, EmbeddingType


@cache
def get_milvus_client() -> MilvusClient:
    config = get_or_creat_settings_ins()
    milvus_client = MilvusClient(
        uri=config.milvus.uri,
        token=f"{config.milvus.user}:{os.getenv('MILVUS__PASSWORD')}",
        db_name=config.milvus.db_name,
        timeout=config.milvus.timeout,
    )
    return milvus_client


def create_collection(collection_name):
    milvus_client = get_milvus_client()
    list_collections = milvus_client.list_collections()
    if collection_name not in list_collections:
        schema = milvus_client.create_schema(
            auto_id=True,
            enable_dynamic_field=True
        )
        schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
        schema.add_field(field_name="source", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="title", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="token", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="text", datatype=DataType.VARCHAR, max_length=2048)
        schema.add_field(field_name="content", datatype=DataType.VARCHAR, max_length=65535)
        schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=768)
        schema.add_field(field_name="keywords", datatype=DataType.VARCHAR, max_length=255, enable_analyzer=True,
                         enable_match=True)

        index_params = milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="IVF_FLAT",
            metric_type="COSINE",
            params={"nlist": 4096}
        )
        milvus_client.create_collection(
            collection_name=collection_name,
            schema=schema,
            index_params=index_params
        )
        logger.info(f"collection {collection_name} created")


def delete_by_source(collection_name: str, source: str):
    results = get_milvus_client().query(
        collection_name=collection_name,
        filter=f"source=='{source}'",
        output_fields=["id"]
    )
    logger.info(f"delete file: {source}")
    if len(results) > 0:
        ids = [data["id"] for data in results]
        get_milvus_client().delete(
            collection_name=collection_name,
            ids=ids
        )
    if not collection_name.endswith("_paragraph"):
        delete_by_source(collection_name + "_paragraph", source)


def hybrid_search_optimized(query_text: str, collection_name: str, keywords: str = "") -> List[Document]:
    try:
        return get_document(query_text, collection_name, keywords)
    except Exception as e:
        logger.error(f"hybrid search error: {e}")
    return []


def _perform_vector_search(milvus_client: MilvusClient, collection_name: str, query_text: str, keywords: str) -> List[dict]:
    """执行向量搜索"""
    hits = milvus_client.search(
        collection_name=collection_name,
        data=[get_query_embedding(query_text + " " + keywords)],
        limit=20,
        anns_field="vector",
        search_params={
            "metric_type": "COSINE",
            "params": {
                "nprobe": 1024,
                "radius": 0.5,
            },
        },
    )[0]

    if not hits:
        return []

    return milvus_client.query(
        collection_name=collection_name,
        ids=[hit['id'] for hit in hits],
        output_fields=["id", "title", "text", "source", "token", "content"]
    )


def _perform_fulltext_search(milvus_client: MilvusClient,
                             collection_name: str, query_text: str, keywords: str) -> List[dict]:
    """执行全文搜索"""
    if not keywords:
        logger.warning(f"No keywords extracted from query: {query_text}")
        return []

    logger.info(f"Performing full-text search with keywords: {keywords}")

    try:
        filter_expr = f"TEXT_MATCH(keywords, '{keywords}')"
        results = milvus_client.query(
            collection_name=collection_name,
            filter=filter_expr,
            limit=10,
        )
        return results
    except Exception as e:
        logger.error(f"Full-text search failed: {e}")
        return []


def _get_full_document_by_token(collection_name: str, token: str) -> Dict:
    """根据token从完整文档集合中获取文档"""
    milvus_client = get_milvus_client()
    try:
        results = milvus_client.query(
            collection_name=collection_name,
            filter=f"token=='{token}'",
            output_fields=["id", "title", "text", "source", "token", "content"],
            limit=1
        )
        if results:
            return results[0]
    except Exception as e:
        logger.error(f"Error getting full document by token {token}: {e}")
    return {}


def _apply_rerank_and_filter(query_text: str, results: List[dict], score_threshold: float = 0.85) -> \
List[dict]:
    """应用重排序和分数过滤"""
    if not results:
        return []

    rerank_texts = [f"{result['title']} \n {result['text']}" for result in results]
    rerank_result = rerank(query_text, rerank_texts)

    top_indices = [item['index'] for item in rerank_result if item['score'] > score_threshold]
    if not top_indices:
        logger.warning(f"No results matched with score_threshold {score_threshold}, lowering threshold to -0.1")
        score_threshold -= 0.1
        top_indices = [item['index'] for item in rerank_result if item['score'] > score_threshold]
        if top_indices:
            top_indices = [top_indices[0]]

    return [results[i] for i in top_indices]


def _filter_duplicate_titles(results: List[dict]) -> List[dict]:
    """
    过滤重复标题的文档，优先保留wiki版本，过滤掉docx版本
    """
    if not results:
        return results

    # 按标题分组
    title_groups = defaultdict(list)
    for result in results:
        title = result.get('title', '').strip()
        if title:
            title_groups[title].append(result)

    filtered_results = []

    for title, docs in title_groups.items():
        if len(docs) == 1:
            # 只有一个文档，直接保留
            filtered_results.extend(docs)
        else:
            # 有多个相同标题的文档，优先保留wiki版本
            wiki_docs = [doc for doc in docs if 'wiki' in doc.get('source', '')]
            docx_docs = [doc for doc in docs if 'docx' in doc.get('source', '')]

            if wiki_docs:
                # 如果有wiki版本，只保留wiki版本
                filtered_results.extend(wiki_docs)
                logger.info(f"Filtered out {len(docx_docs)} docx versions for title: {title}, kept {len(wiki_docs)} wiki versions")
            else:
                # 如果没有wiki版本，保留所有文档
                filtered_results.extend(docs)

    return filtered_results


def _convert_to_documents(collection_name: str, results: List[dict]) -> List[Document]:
    """
    将查询结果转换为Document对象，实现智能合并逻辑：
    - 如果来自同一个文档的段落有2块，合并它们
    - 如果超过2块，则从完整文档集合中获取完整文档替换这些段落
    """
    if not results:
        return []

    if collection_name.endswith("_paragraph"):
        collection_name = collection_name.split("_paragraph")[0]
    
    # 先过滤重复标题的文档，优先保留wiki版本，过滤掉docx版本
    filtered_results = _filter_duplicate_titles(results)

    # 按token分组段落
    token_groups = defaultdict(list)
    for result in filtered_results:
        token = result.get('token', '')
        if token:
            token_groups[token].append(result)
        else:
            # 如果没有token，直接作为独立文档处理
            token_groups[f"no_token_{result.get('id', '')}"] = [result]

    final_documents = []

    for token, paragraphs in token_groups.items():
        if token.startswith('no_token_'):
            # 没有token的文档，直接处理
            result = paragraphs[0]
            doc = Document(
                page_content=result['content'],
                metadata={
                    "id": result['id'],
                    "title": result['title'],
                    "source": result['source']
                }
            )
            final_documents.append(doc)

        elif len(paragraphs) == 1:
            result = paragraphs[0]
            doc = Document(
                page_content=result['content'],
                metadata={
                    "id": result['id'],
                    "title": result['title'],
                    "source": result['source'],
                    "token": result.get('token', '')
                }
            )
            final_documents.append(doc)

        elif len(paragraphs) == 2:
            logger.info(f"Merging 2 paragraphs from document token: {token}")
            merged_text = "\n\n".join([p['content'] for p in paragraphs])
            first_paragraph = paragraphs[0]
            doc = Document(
                page_content=merged_text,
                metadata={
                    "id": f"merged_{first_paragraph['id']}",
                    "title": first_paragraph['title'],
                    "source": first_paragraph['source'],
                    "token": token,
                    "merged_from": [p['id'] for p in paragraphs]
                }
            )
            final_documents.append(doc)

        else:
            logger.info(f"Getting full document for token: {token} (found {len(paragraphs)} paragraphs)")

            full_doc = _get_full_document_by_token(collection_name, token)

            if full_doc:
                doc = Document(
                    page_content=full_doc['content'],
                    metadata={
                        "id": full_doc['id'],
                        "title": full_doc['title'],
                        "source": full_doc['source'],
                        "token": full_doc.get('token', ''),
                        "replaced_paragraphs": [p['id'] for p in paragraphs]
                    }
                )
                final_documents.append(doc)
            else:
                logger.warning(f"Could not get full document for token: {token}, merging all paragraphs")
                merged_text = "\n\n".join([p['content'] for p in paragraphs])

                first_paragraph = paragraphs[0]
                doc = Document(
                    page_content=merged_text,
                    metadata={
                        "id": f"merged_all_{first_paragraph['id']}",
                        "title": first_paragraph['title'],
                        "source": first_paragraph['source'],
                        "token": token,
                        "merged_from": [p['id'] for p in paragraphs]
                    }
                )
                final_documents.append(doc)

    return final_documents


def get_document(intent: str, collection_name: str, keywords: str = "") -> List[Document]:
    paragraph_collection_name = collection_name + "_paragraph"
    milvus_client = get_milvus_client()

    # 同时在 paragraph 和完整文档集合中搜索
    results_paragraph = _search_and_process(milvus_client, paragraph_collection_name, intent, keywords)

    # 合并 paragraph 和 full doc 的结果（注意去重）
    combined_docs = []
    seen_tokens = set()

    if results_paragraph:
        for doc in results_paragraph:
            token = doc.metadata.get("token")
            if token and token in seen_tokens:
                continue
            seen_tokens.add(token)
            combined_docs.append(doc)
    else:
        results_full_doc = _search_and_process(milvus_client, collection_name, intent, keywords)
        if results_full_doc:
            for doc in results_full_doc:
                token = doc.metadata.get("token")
                if token and token in seen_tokens:
                    continue
                seen_tokens.add(token)
                combined_docs.append(doc)

    if not combined_docs:
        logger.warning(f"No results found in any search stage for query: {intent}")
        return []

    return combined_docs


def _search_and_process(
        milvus_client: MilvusClient,
        collection_name: str,
        intent: str,
        keywords: str = "",
) -> Optional[List[Document]]:
    # 同时执行向量搜索和全文搜索
    vector_results = _perform_vector_search(milvus_client, collection_name, intent, keywords)
    fulltext_results = _perform_fulltext_search(milvus_client, collection_name, intent, keywords)

    # 合并结果（注意去重）
    combined_results = []
    seen_ids = set()

    for result in (vector_results + fulltext_results):
        if result.get("id") not in seen_ids:
            seen_ids.add(result["id"])
            combined_results.append(result)

    if not combined_results:
        return None

    filtered_results = _apply_rerank_and_filter(intent, combined_results)
    if not filtered_results:
        return None

    return _convert_to_documents(collection_name, filtered_results)


def get_query_embedding(query_text: str) -> list[float]:
    config = get_or_creat_settings_ins()
    embedding = LLMFactory(config.embedding).BuildEmbedding(EmbeddingType.HUGGINGFACE)
    return embedding.embed_query(query_text)
