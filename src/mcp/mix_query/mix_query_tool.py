"""
融合查询工具
支持文档和资源的并发查询与结果聚合
"""
import asyncio
import concurrent
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Type

from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.documents import Document
from langchain_core.tools import BaseTool
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.types import TextContent, ImageContent, EmbeddedResource
from pydantic import Field, BaseModel

from src.log import logger
from .aggregator import ResultAggregator
from .intent_parser import IntentParser
from .template_selector import TemplateSelector
from ...helper.client.milvus import hybrid_search_optimized


class MixQueryArgs(BaseModel):
    query: str = Field(..., description="CMDB 资源名称或 IP 地址，或具体问题或关键词")


class MixQueryTool(BaseTool):
    """融合查询工具"""

    name: str = "mix_query"
    args_schema: Type[BaseModel] = MixQueryArgs
    return_direct: bool = True
    description: str = '''
- 功能说明
该工具集成了对 CMDB（配置管理数据库）中多种资源信息的查询，以及公司知识库平台的智能查询能力，帮助用户快速获取综合信息，并进行融合汇总回答。

可查询内容范围：
1. CMDB 资源
    - 业务类：应用、CMD 命令号、Feature Team
    - 计算类：主机、云大数据集群、超级节点、机器学习平台资源组
    - 数据库：ES 搜索引擎、MongoDB、MySQL、Redis、PostgreSQL、GraphDB、TiDB、Guass Redis、Doris、KeeWiDB、PolarDBX
    - 中间件：Kafka Topic、RabbitMQ、Kafka 集群、Etcd、Kafka 集群消费组、RocketMQ
    - 网络类：负载均衡监听器、负载均衡、公网入口、VPC、NAT 网关、VPN 网关、VPN 通道、专线接入、专线通道、云联网、专线网关、云联网通道、子网
    - 存储类：云硬盘、Bucket、文件存储
    - CDN 与域名：域名、CDN、主域名
    - 其他：NginxRouter、应用运行时数据、Region、日志主题、APM、自建主机

2. 公司知识库
    可以帮助用户在数据库、IT和SRE领域快速获取所需的知识和解决方案，以支持其日常工作和专业发展
    - 数据库知识：提供对涉及数据库的使用指南、云服务资源选型、数据库规范、故障预案等各类问题的详细信息查询，帮助用户获得必要的数据库管理和维护知识。
    - SRE 运维知识：涵盖运维操作的教程、内部流程与规范、公司政策、产品信息、以及故障解决指南，协助用户在SRE领域高效处理相关事务并遵循公司规定。
    - IT 支撑知识：提供各种内部IT问题的解决方案，包括软件教程、设备故障排查、网络和VPN问题的处理指南，确保用户能快速获取和实施有效的IT技术支持。

输入参数：
- query：CMDB 资源名称或 IP 地址，或具体问题或关键词

核心特性：
- 支持多种资源类型的详细信息查询与知识库的广泛内容查询
- 能够综合、智能地处理输入，提供针对性的精确信息 

注意事项：
- 此工具仅能查询规定类型的资源及知识领域
- 请确保查询内容明确属于 CMDB 管理或知识平台范围内

典型场景：
- 需要获取特定 CMDB 资源的详细配置信息时，可通过该工具输入资源名称或 IP 地址
- 需要获得专业知识解答和操作指南支持时，可输入具体问题或关键词以快速查询
'''
    collection_name: str

    def __init__(self, collection_name: str, **kwargs):
        super().__init__(collection_name=collection_name, **kwargs)

    def _run(
            self,
            query: str,
            run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        try:
            logger.info(f"开始融合查询: {query}")

            parsed_intents, response_template = self.process_query(query)

            if not parsed_intents:
                return "无法解析用户意图，请重新描述您的问题"

            # 2. 检查是否包含资源查询
            has_resources = self._check_has_resources(parsed_intents)

            # 3. 执行查询
            if has_resources:
                doc_results, resource_results = self._execute_concurrent_queries(parsed_intents)
            else:
                doc_results = self._execute_document_queries(parsed_intents)
                resource_results = []

            # 4. 聚合结果
            final_answer = self._aggregate_results(query, parsed_intents, doc_results, resource_results,
                                                   response_template)

            logger.info("融合查询完成")
            return final_answer

        except Exception as e:
            logger.error(f"融合查询异常: {e}")
            return f"查询过程中出现异常: {str(e)}"

    def process_query(self, query: str) -> tuple[Any, Any]:
        """
        处理查询的主函数，使用并发执行解析用户意图和选择回答模板。
        """
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            intent_future = executor.submit(self._parse_user_intent, query)
            template_future = executor.submit(self._select_response_template, query)

            parsed_intents = intent_future.result()
            selected_template = template_future.result()
        logger.info(f"选择的回答模板: {selected_template}")

        # 根据解析的意图和选择的模板生成回答
        return parsed_intents, selected_template

    @staticmethod
    def _select_response_template(query: str) -> str:
        """选择回答模板"""
        try:
            logger.info(f"选择回答模板: {query}")
            template_selector = TemplateSelector()
            selected_template_id = template_selector.select_template(query)

            logger.info(f"选择的回答模板: {selected_template_id}")

            # 获得模版内容： selected_template
            selected_template = template_selector.get_template_content(selected_template_id)

            return selected_template
        except Exception as e:
            logger.error(f"模板选择失败: {e}")
            return "answer_default_tmpl"

    @staticmethod
    def _parse_user_intent(query: str) -> List[Dict[str, Any]]:
        """解析用户意图"""
        try:
            logger.info(f"解析用户意图: {query}")
            intent_parser = IntentParser()
            parsed_intents = intent_parser.parse(query)

            if parsed_intents:
                logger.info(f"解析到的意图: {parsed_intents}")
            else:
                logger.warning("未能解析到有效意图")

            return parsed_intents
        except Exception as e:
            logger.error(f"意图解析失败: {e}")
            return []

    @staticmethod
    def _check_has_resources(parsed_intents: List[Dict[str, Any]]) -> bool:
        """检查是否包含资源查询"""
        try:
            has_resources = any(
                intent.get('resources') and len(intent['resources']) > 0
                for intent in parsed_intents
            )
            logger.info(f"是否包含资源查询: {has_resources}")
            return has_resources
        except Exception as e:
            logger.error(f"资源检查失败: {e}")
            return False

    def _execute_document_queries(self, parsed_intents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行文档查询"""
        try:
            logger.info("执行文档查询")
            doc_results = []

            for intent in parsed_intents:
                result = self._query_knowledge_base(intent)
                if result:
                    doc_results.append({
                        'intent': intent,
                        'result': result
                    })

            logger.info(f"文档查询完成")
            return doc_results
        except Exception as e:
            logger.error(f"文档查询失败: {e}")
            return []

    def _execute_concurrent_queries(self, parsed_intents: List[Dict[str, Any]]) -> tuple[
        List[Dict[str, Any]], List[Dict[str, Any]]]:
        """执行并发文档和资源查询"""
        try:
            logger.info("执行并发文档和资源查询")
            doc_results = []
            resource_results = []

            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_type = {}

                # 提交知识库查询任务
                for intent in parsed_intents:
                    future = executor.submit(self._query_knowledge_base, intent)
                    future_to_type[future] = ('doc', intent)

                    # 提交CMDB查询任务
                    if intent.get('resources'):
                        for resource in intent['resources']:
                            future = executor.submit(self._query_cmdb, resource)
                            future_to_type[future] = ('resource', resource)

                # 收集结果
                for future in as_completed(future_to_type):
                    query_type, query_info = future_to_type[future]
                    try:
                        result = future.result()
                        if query_type == 'doc':
                            if result:
                                doc_results.append({
                                    'intent': query_info,
                                    'result': result
                                })
                        else:  # resource
                            if result:
                                resource_results.append({
                                    'resource': query_info,
                                    'result': result
                                })
                    except Exception as e:
                        logger.error(f"查询失败 {query_type} - {query_info}: {e}")

            logger.info(f"并发查询完成，文档结果: {len(doc_results)}, 资源结果: {len(resource_results)}")
            return doc_results, resource_results
        except Exception as e:
            logger.error(f"并发查询失败: {e}")
            return [], []

    @staticmethod
    def _aggregate_results(query: str, parsed_intents: List[Dict[str, Any]],
                           doc_results: List[Dict[str, Any]], resource_results: List[Dict[str, Any]],
                           response_template: str) -> str:
        """聚合查询结果"""
        try:
            logger.info("执行结果聚合")
            aggregator = ResultAggregator()
            final_answer = aggregator.aggregate(
                original_query=query,
                parsed_intents=parsed_intents,
                doc_results=doc_results,
                resource_results=resource_results,
                response_template=response_template
            )
            logger.info("结果聚合完成")
            return final_answer
        except Exception as e:
            logger.error(f"结果聚合失败: {e}")
            return f"结果聚合失败: {str(e)}"

    def _query_knowledge_base(self, intent: Dict[str, Any]) -> List[Document]:
        """查询知识库"""
        try:
            query_text = intent['intention']
            keywords = intent['keywords']
            return hybrid_search_optimized(query_text, self.collection_name, keywords)
        except Exception as e:
            logger.error(f"知识库查询失败: {e}")
            return []

    @staticmethod
    def _query_cmdb(resource: str) -> Optional[List[TextContent | ImageContent | EmbeddedResource]]:
        """查询CMDB资源"""

        async def _async_query_cmdb():
            try:
                async with sse_client(url="https://tt-telemetry.ttyuyin.com/sse") as (read, write):
                    session = ClientSession(read, write)
                    async with session:
                        await session.initialize()
                        result = await session.call_tool("query_cmdb", {"query": resource})
                        logger.info(f"query_cmdb result={result.content}")
                        return result.content[0].text
            except Exception as e:
                logger.error(f"CMDB查询失败 {resource}: {e}")
                return None

        try:
            return asyncio.run(_async_query_cmdb())
        except Exception as e:
            logger.error(f"CMDB查询执行失败 {resource}: {e}")
            return None
