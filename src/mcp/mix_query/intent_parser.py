from typing import List, Dict, Any
from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import SimpleJsonOutputParser
from langchain_core.prompts import PromptTemplate
from src.helper.client.model import get_chat_model
from src.log import logger


class IntentParser:
    """用户意图解析器，使用大模型分解用户问题"""

    def __init__(self):
        self.llm = get_chat_model()
        self.parser = OutputFixingParser(retry_chain=self.llm, parser=SimpleJsonOutputParser())
        self.prompt_template = self._create_prompt_template()

    def _create_prompt_template(self) -> PromptTemplate:
        """创建意图解析的提示模板"""
        template = """
===== 用户问题 =====
{query}

===== 任务说明 =====
请分析用户问题，提取用户的查询意图、关键词和相关资源信息。

===== 资源类型定义 =====
如果用户问题中包含以下类型资源，请提取出来资源名称或属性列表：
**业务类**：应用、cmd命令号、feature_team
**计算类**：主机、云大数据集群、超级节点、机器学习平台资源组
**数据库**：ES搜索引擎、Mongodb、MYSQL、Redis、PostgreSQL、GraphDB、TIDB、guass_redis、Doris、KeeWiDB、Polardbx
**中间件**：KAFKA TOPIC、RabbitMQ、KAFKA集群、etcd、kafka集群消费组、RocketMQ
**网络类**：负载均衡监听器、负载均衡、公网入口、VPC、NAT网关、VPN网关、VPN通道、专线接入、专线通道、云联网、专线网关、云联网通道、子网
**存储类**：云硬盘、BUCKET、文件存储
**CDN与域名**：域名、CDN、主域名
**其他**：NginxRouter、应用运行时数据、Region、日志主题、APM、自建主机

===== 分析要求 =====
1. **意图(intention)**：提取用户想要了解或操作的对象，例如：日志规范是什么，提取：日志规范，如何申请主机权限，提取：申请主机权限。
2. **关键词(keywords)**：从意图中提取操作对象和要进行的操作组合，最少五个，例如：日志规范、权限申请、主机权限、申请权限这种复合关键词
3. **资源(resources)**：如果问题涉及具体的资源名称或属性，禁止填写资源类型例如 Mongodb、MYSQL 这种资源类型是禁止的。

===== 输出格式 =====
请严格按照以下JSON数组格式返回分析结果，如果包含多个意图或子意图就提取多个：

[
    {{
        "intention": "用户的查询意图描述",
        "keywords": "关键词 关键词 关键词 ...",
        "resources": ["资源名称1", "资源名称2"]
    }}
]

===== 示例 =====
用户问题：我想知道 hw-bj-xp-rtmp-lx-01 主机是干什么的？
分析结果：
[
    {{
        "intention": "主机用途",
        "keywords": "主机用途 主机功能 主机用途",
        "resources": ["hw-bj-xp-rtmp-lx-01"]
    }}
]

用户问题：如何配置Redis集群的高可用？
分析结果：
[
    {{
        "intention": "配置Redis集群高可用",
        "keywords": "Redis高可用 Redis配置 Redis集群 集群高可用",
        "resources": []
    }}
]

用户问题：10.203.2.37这台服务器的监控告警怎么设置？
分析结果：
[
    {{
        "intention": "服务器监控告警设置",
        "keywords": "服务器监控 监控告警 告警设置 服务器监控告警",
        "resources": ["10.203.2.37"]
    }}
]
"""
        return PromptTemplate(template=template, input_variables=["query"])

    def parse(self, query: str) -> List[Dict[str, Any]]:
        """
        解析用户查询意图

        Args:
            query: 用户查询问题

        Returns:
            解析后的意图列表，每个意图包含intention、keywords、resources字段
        """
        try:
            # 格式化提示
            formatted_prompt = self.prompt_template.format(query=query)

            # 调用LLM
            llm_response = self.llm.invoke(formatted_prompt)

            # 解析输出
            parsed_result = self.parser.parse(llm_response.content)

            # 验证和清理结果
            if isinstance(parsed_result, list):
                cleaned_results = []
                for intent in parsed_result:
                    if isinstance(intent, dict) and 'intention' in intent:
                        # 确保必要字段存在
                        cleaned_intent = {
                            'intention': intent.get('intention', ''),
                            'keywords': intent.get('keywords', ""),
                            'resources': intent.get('resources', [])
                        }
                        if isinstance(cleaned_intent['resources'], str):
                            cleaned_intent['resources'] = [cleaned_intent['resources']]

                        cleaned_results.append(cleaned_intent)

                if cleaned_results:
                    return cleaned_results

            # 如果解析失败，返回默认结果
            logger.warning(f"意图解析失败，使用默认解析: {query}")
            return []

        except Exception as e:
            logger.error(f"意图解析出错: {e}")
            return []
