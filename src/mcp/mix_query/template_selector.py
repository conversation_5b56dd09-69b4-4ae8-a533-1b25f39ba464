from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import SimpleJsonOutputParser
from langchain_core.prompts import PromptTemplate

from src.helper.client.model import get_chat_model
from src.log import logger


class TemplateSelector:
    """回答模板选择器，使用大模型根据用户问题选择合适的回答模板"""

    def __init__(self):
        self.llm = get_chat_model()
        self.parser = OutputFixingParser(retry_chain=self.llm, parser=SimpleJsonOutputParser())
        self.prompt_template = self._create_prompt_template()

    @staticmethod
    def _create_prompt_template() -> PromptTemplate:
        """创建模板选择的提示模板"""
        template = """
===== 用户问题 =====
{query}

===== 任务说明 =====
请根据用户问题的特点，选择最合适的回答模板。

===== 模板类型说明 =====
1. **answer_step_tmpl** - 步骤流程模板
   ### 问题[必须]
   （简要复述用户问题，明确需求）
   示例：您需要了解如何申请【XXX权限】/解决【XX问题】/处理【XXX故障】的步骤流程。

   ### 步骤 [必须]
   （分步骤描述核心流程，逻辑清晰）
   * 示例：
     步骤1：进入【具体位置/界面】
     操作路径：描述按钮/菜单层级（如：设置 > 账户 > 权限管理）。
     步骤2：提交【申请/操作】
     需填写内容：说明关键字段（如：权限类型、申请理由）。
     步骤3：等待审核/执行验证
     时间周期：说明审核时长或操作生效时间。
     步骤4：确认结果
     验证方式：如何检查是否成功（如：邮件通知、状态变更）。

  ### 注意事项/常见错误 [可选项]
 （列出易错点或需规避的问题）

  ### 补充说明 [可选项]
 （附加文档、相关信息或例外情况）

2. **answer_plan_tmpl** - 方案对比模板
   ## 问题 [必须]
  （明确需求）
   示例：您需要了解【XXX场景/目标】的技术方案及其核心特性。

   ## 方案概览 [必须]
  （分点列出主流方案，每方案包含：核心特性 + 适用场景）

   1. 方案名称（如技术/工具/架构）
      * 核心特性：
         * 特性1（如高性能、低延迟、高兼容性）
         * 特性2（如开源生态、商业支持）
         * 特性3（如学习成本、部署复杂度）
      * 适用场景：
         * 场景1（如大规模实时数据处理）
         * 场景2（如中小项目快速验证）
  ## 总结 [可选]
  (关键信息，特性总结)

3. **answer_info_tmpl** - 资源信息模板
   ## 问题 [必须]
  （明确需求）
   示例：您需要了解【XXX资源】

   ## 信息
   **资源类型[可选]**
   (服务器/虚拟机/网络设备/存储设备/...)
   **基础[可选]**
   (名称, 载体, 系统类型，产品类型)
   **硬件[可选]**
   (型号, 配置, 磁盘, 内存, cpu)
   **网络[可选]**
   (IP列表, 网络拓扑, 网络设备, 安全组...)
   **业务信息[可选]**
   (业务线，归属部门，负责人，...)
   **其他[可选]**
   (其他信息, 如: 备注, 附件, ...)

4. **answer_default_tmpl** - 默认通用模板
   ## 问题 [必须]
  （明确需求）  
   示例：您需要了解【XXX场景/目标】的技术方案及其核心特性。

   ## 内容 [必须]
  （使用 markdown 格式组织答案并突出信息重点及层级）

===== 分析要求 =====
1. 仔细分析用户问题的核心意图
2. 识别问题中的关键动词和名词
3. 判断用户是想了解操作步骤、方案对比、资源信息还是一般知识
4. 选择最匹配的模板类型

===== 输出格式 =====
请严格按照以下JSON格式返回分析结果：

{{
    "template_id": "模板ID（answer_step_tmpl/answer_plan_tmpl/answer_info_tmpl/answer_default_tmpl）",
    "reason": "选择该模板的原因说明"
}}

===== 示例 =====
用户问题：如何申请数据库权限？
分析结果：
{{
    "template_id": "answer_step_tmpl",
    "reason": "用户询问申请权限的具体操作步骤，需要分步骤说明流程"
}}

用户问题：数据库选型有哪些方案？
分析结果：
{{
    "template_id": "answer_plan_tmpl", 
    "reason": "用户询问技术选型方案，需要对比不同方案的特性和适用场景"
}}

用户问题：10.203.2.37这台服务器的配置信息
分析结果：
{{
    "template_id": "answer_info_tmpl",
    "reason": "用户询问具体服务器的详细配置信息"
}}

用户问题：什么是微服务架构？
分析结果：
{{
    "template_id": "answer_default_tmpl",
    "reason": "用户询问概念性知识，适合用通用模板进行解释说明"
}}
"""
        return PromptTemplate(template=template, input_variables=["query"])

    def select_template(self, query: str) -> str:
        """
        选择合适的回答模板

        Args:
            query: 用户查询问题

        Returns:
            包含template_id和reason的字典
        """
        try:
            logger.info(f"开始选择回答模板: {query}")

            # 格式化提示
            formatted_prompt = self.prompt_template.format(query=query)

            # 调用LLM
            llm_response = self.llm.invoke(formatted_prompt)

            # 解析输出
            parsed_result = self.parser.parse(llm_response.content)

            # 验证结果
            if isinstance(parsed_result, dict) and 'template_id' in parsed_result:
                template_id = parsed_result.get('template_id', 'answer_default_tmpl')

                # 验证模板ID是否有效
                valid_templates = [
                    'answer_step_tmpl',
                    'answer_plan_tmpl',
                    'answer_info_tmpl',
                    'answer_default_tmpl'
                ]

                if template_id not in valid_templates:
                    logger.warning(f"无效的模板ID: {template_id}，使用默认模板")
                    template_id = 'answer_default_tmpl'

                return template_id
            else:
                logger.warning("模板选择解析失败，使用默认模板")
                return "answer_default_tmpl"
        except Exception as e:
            logger.error(f"模板选择出错: {e}")
            return "answer_default_tmpl"

    @staticmethod
    def get_template_content(template_id: str) -> str:
        """获取模板内容"""
        if template_id == 'answer_step_tmpl':
            return '''
   （分步骤描述核心流程，逻辑清晰 必填）
   * 示例
     - 步骤1：进入【具体位置/界面】
       操作路径：描述按钮/菜单层级（如：设置 > 账户 > 权限管理）。
     - 步骤2：提交【申请/操作】
       需填写内容：说明关键字段（如：权限类型、申请理由）。
     - 步骤3：等待审核/执行验证
       时间周期：说明审核时长或操作生效时间。
     - 步骤4：确认结果
       验证方式：如何检查是否成功（如：邮件通知、状态变更）。
            '''
        elif template_id == 'answer_plan_tmpl':
            return '''
  （分点列出主流方案，每方案包含：核心特性 + 适用场景, 必填）
   * 示例：
   1. 方案名称（如技术/工具/架构）
      * 核心特性：
         * 特性1（如高性能、低延迟、高兼容性）
         * 特性2（如开源生态、商业支持）
         * 特性3（如学习成本、部署复杂度）
      * 适用场景：
         * 场景1（如大规模实时数据处理）
         * 场景2（如中小项目快速验证）
  **总结**
  (关键信息，特性总结, 选填)
            '''
        elif template_id == 'answer_info_tmpl':
            return '''
   (列出用户想要查询的对应的资源属性，要根据用户的问题对资源属性进行筛选，必填)
    按照资源属性类型进行分组，在展示每个属性，使用markdown列表展示
            '''
        else:
            return '''
   **内容**
  （使用 markdown 格式组织答案并突出信息重点及层级, 必填）
   采用总分形式说明，或者使用列表形式，不要讲内容平铺回答
            '''
